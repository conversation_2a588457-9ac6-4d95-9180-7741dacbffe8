#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从KdConv数据集中提取三个领域的对话句子
每个领域提取100条句子，总共300条
输出为CSV和JSON格式
"""

import json
import csv
import os
from typing import List, Dict, Any

def extract_sentences_from_domain(file_path: str, domain: str, num_sentences: int = 100) -> List[str]:
    """
    从指定领域的train.json文件中提取句子
    
    Args:
        file_path: JSON文件路径
        domain: 领域名称
        num_sentences: 需要提取的句子数量
    
    Returns:
        提取的句子列表
    """
    sentences = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"正在从 {domain} 领域提取句子...")
        print(f"文件 {file_path} 包含 {len(data)} 个对话")
        
        # 遍历所有对话
        for conversation in data:
            if len(sentences) >= num_sentences:
                break
                
            # 遍历对话中的所有消息
            messages = conversation.get('messages', [])
            for message in messages:
                if len(sentences) >= num_sentences:
                    break
                    
                # 提取message字段中的句子
                sentence = message.get('message', '').strip()
                if sentence:  # 确保句子不为空
                    sentences.append(sentence)
        
        print(f"从 {domain} 领域成功提取 {len(sentences)} 条句子")
        return sentences[:num_sentences]  # 确保不超过指定数量
        
    except FileNotFoundError:
        print(f"错误：找不到文件 {file_path}")
        return []
    except json.JSONDecodeError:
        print(f"错误：无法解析JSON文件 {file_path}")
        return []
    except Exception as e:
        print(f"错误：处理文件 {file_path} 时发生异常: {e}")
        return []

def save_to_csv(data: List[Dict[str, Any]], filename: str):
    """
    将数据保存为CSV格式
    
    Args:
        data: 要保存的数据列表
        filename: 输出文件名
    """
    try:
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['index', 'source', 'sentence']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            writer.writeheader()
            for row in data:
                writer.writerow(row)
        
        print(f"CSV文件已保存: {filename}")
    except Exception as e:
        print(f"保存CSV文件时发生错误: {e}")

def save_to_json(data: List[Dict[str, Any]], filename: str):
    """
    将数据保存为JSON格式
    
    Args:
        data: 要保存的数据列表
        filename: 输出文件名
    """
    try:
        with open(filename, 'w', encoding='utf-8') as jsonfile:
            json.dump(data, jsonfile, ensure_ascii=False, indent=2)
        
        print(f"JSON文件已保存: {filename}")
    except Exception as e:
        print(f"保存JSON文件时发生错误: {e}")

def main():
    """主函数"""
    # 定义三个领域的数据路径
    domains = {
        'film': 'data/film/train.json',
        'music': 'data/music/train.json', 
        'travel': 'data/travel/train.json'
    }
    
    # 检查数据目录是否存在
    if not os.path.exists('data'):
        print("错误：找不到data目录，请确保脚本在正确的目录下运行")
        return
    
    all_sentences = []
    index = 1
    
    # 从每个领域提取100条句子
    for domain, file_path in domains.items():
        print(f"\n{'='*50}")
        print(f"处理 {domain} 领域")
        print(f"{'='*50}")
        
        sentences = extract_sentences_from_domain(file_path, domain, 100)
        
        # 将句子添加到总列表中
        for sentence in sentences:
            all_sentences.append({
                'index': index,
                'source': 0,
                'sentence': sentence
            })
            index += 1
    
    print(f"\n{'='*50}")
    print(f"提取完成！总共提取了 {len(all_sentences)} 条句子")
    print(f"{'='*50}")
    
    # 保存为CSV和JSON格式
    if all_sentences:
        save_to_csv(all_sentences, 'kdconv.csv')
        save_to_json(all_sentences, 'kdconv.json')
        
        # 显示一些统计信息
        print(f"\n统计信息:")
        print(f"- 总句子数: {len(all_sentences)}")
        print(f"- 每个领域: 100条句子")
        print(f"- 输出文件: extracted_sentences.csv, extracted_sentences.json")
        
        # 显示前几条示例
        print(f"\n前5条句子示例:")
        for i, item in enumerate(all_sentences[:5]):
            print(f"{item['index']}: {item['sentence']}")
    else:
        print("没有提取到任何句子！")

if __name__ == "__main__":
    main()
